import {<PERSON>mpo<PERSON>, ElementRef, <PERSON>Dev<PERSON>ode, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {IonicModule} from "@ionic/angular";
import {DrawingService} from "../../services/drawing.service";
import {Wall} from "../../../models/drawing/wall.model";
import {Coordinate, Graph} from "../../../stores/planning/graph.model";
import {getLineLength} from "../../../utils/line";
import {NgIf} from "@angular/common";
import {Color, CompoundPath, Path, Point, PointText, Project, Rectangle, Size} from "paper";
import {calculateCurrentWallAngle} from "../../../utils/angle";
import {drawAndUnitePaths} from "../../../models/paperjs/utils";
import {DimensionService} from '../../services/dimension.service';
import {HeaderComponent} from "../../header/header.component";
import {FormsModule} from "@angular/forms";
import {Subscription} from "rxjs";
import {PlannerStorageService} from "../../services/dexie/planner-storage.service";
import {ActivatedRoute} from "@angular/router";
import {TablesUpdate} from "../../../models/database.types";
import {blobToBase64} from "../../../utils/image";
import {PlanningLocal} from "../../services/dexie/dexie-db.service";
import {SupabasePlanningService} from "../../services/supabase/supabase-planning.service";

@Component({
  selector: 'app-drawing',
  templateUrl: './drawing.component.html',
  styleUrls: ['./drawing.component.scss'],
  standalone: true,
  imports: [IonicModule, NgIf, HeaderComponent, FormsModule],
})

export class DrawingComponent implements OnInit, OnDestroy {
  @ViewChild('drawingCanvas', {static: true}) canvasRef!: ElementRef<HTMLCanvasElement>;
  public canvas?: HTMLCanvasElement;
  window = window;
  // @ts-ignore
  project: Project;
  private ctx!: CanvasRenderingContext2D;
  private scale = 1;
  private readonly MAX_ZOOM = 5;
  private readonly MIN_ZOOM = 0.1;
  private isDrawing = false;
  private isLocked = false;

  private WALL_THICKNESS = 20;
  private WALL_HEIGHT = 260;
  private readonly MIN_LENGTH = 10;
  private readonly SNAP_RADIUS = 45;
  private readonly ANGLE_SNAP = 5;

  private wallThicknessSubscription: Subscription | null = null;

  eyeMenuOpen = false;
  showGuidelines = true;
  showAngles = true;
  showMeasurements = true;
  currentMode: 'draw' | 'edit' = 'draw';
  devMode = false;
  isValidAngle = true;

  private wallFillColor = new Color(0.8, 0.8, 0.8);
  private wallStrokeColor = new Color(0, 0, 0);
  private readonly GUIDE_LINE_COLOR = '#dd3333';

  // @ts-ignore
  protected originalPaths: Path[] = [];
  private pathConnections: {
    // @ts-ignore
    path: Path,
    startConnection: boolean,
    endConnection: boolean,
    middleConnection: boolean,
    relativeAngle: number
  }[] = [];
  // @ts-ignore
  protected undoneOriginalPaths: Path[] = [];
  private undonePathConnections: {
    // @ts-ignore
    path: Path,
    startConnection: boolean,
    endConnection: boolean,
    middleConnection: boolean,
    relativeAngle: number
  }[] = [];

  protected startX = 0;
  protected startY = 0;
  // @ts-ignore
  startPoint: Point;
  // @ts-ignore
  protected path: Path;
  protected paths: any[] = []
  // @ts-ignore
  private closestPath: Path
  // @ts-ignore
  private currentRect: Rectangle;
  // @ts-ignore
  private angleWheelPosition?: Point;
  private selectedAngle: number = 0;
  private relativeAngle = 0;
  private startConnection = false;
  private endConnection = false;
  private middleConnection = false;
  // @ts-ignore
  private guideLines: Path[] = [];

  //Moving canvas around
  private isPanning = false;
  // @ts-ignore
  private lastPanPoint: Point | null = null;

  // @ts-ignore
  private clickStartPosition: Point | null = null;
  private readonly DRAG_THRESHOLD = 5;
  selectedPaths: Set<any> = new Set();
  private selectedPathHighlights: Map<any, any> = new Map();

  // Edit mode
  private isDraggingPath = false;
  // @ts-ignore
  private dragOffset: Point | null = null;
  // @ts-ignore
  private draggedOriginalPath: Path | null = null;
  private draggedPathIndex: number = -1;
  // @ts-ignore
  private dragStartPoint: Point | null = null;

  private dimensionLines: any[] = [];
  private permanentDimensionLines: any[] = [];
  private innerDimensionLines: any[] = [];
  // @ts-ignore
  private permanentAngles: any[] = [];

  private graph: Graph = new Graph();
  private graphs: Map<string, Graph> = new Map();
  walls: Map<string, Wall> = new Map();

  planningInfo?: TablesUpdate<'planning_infos'>;
  planningId?: string;
  planning?: PlanningLocal

  private touchStartPoints: { x: number, y: number }[] = [];
  private lastTouchDistance: number = 0;
  private isPinching = false;

  constructor(
    private drawingService: DrawingService,
    private dimensionService: DimensionService,
    private plannerStorageService: PlannerStorageService,
    private route: ActivatedRoute,
    private planningService: SupabasePlanningService
  ) {
  }

  async ngOnInit() {
    this.route.queryParams.subscribe(async p => {
      this.planningId = p['id'];
      this.planningInfo = await this.plannerStorageService.getPlanningInfo(p['id']);

      const {data} = await this.planningService.getPlanningById(this.planningId!);
      this.planning = data;

      if (!this.planning) {
        this.planning = await this.plannerStorageService.getPlanningById(p['id']);
      }

      this.WALL_THICKNESS = this.planningInfo?.wall_thickness ?? 20;
      this.redrawWithNewWallThickness();

      if (this.canvas && this.canvasRef.nativeElement) {
        this.canvas = this.canvasRef.nativeElement;
        this.ctx = this.canvas.getContext('2d')!;
        this.devMode = isDevMode()

        // Ensure canvas is properly sized
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;

        this.project = new Project(this.canvas);

        // Set up the view properly
        this.project.view.viewSize = new Size(this.canvas.width, this.canvas.height);
      }
      this.drawSavedUnitedPaths()
    });
  }

  ngOnDestroy() {
    // Cleanup subscriptions to prevent memory leaks
    if (this.wallThicknessSubscription) {
      this.wallThicknessSubscription.unsubscribe();
    }
  }

  async ionViewDidEnter() {
    if (this.canvasRef && this.canvasRef.nativeElement) {
      this.canvas = this.canvasRef.nativeElement;

      // Ensure canvas is properly sized
      this.canvas.width = window.innerWidth;
      this.canvas.height = window.innerHeight;

      this.project = new Project(this.canvas);
      this.project.view.viewSize = new Size(this.canvas.width, this.canvas.height);

      await this.drawSavedUnitedPaths()
    }
  }

  // Method to redraw everything with the new wall thickness
  private redrawWithNewWallThickness() {
    // Clear the project
    this.project.clear();

    // Clear dimension lines and angles
    this.permanentDimensionLines.forEach(line => line.remove());
    this.innerDimensionLines.forEach(line => line.remove());
    this.permanentAngles.forEach(angle => angle.remove());

    this.permanentDimensionLines = [];
    this.innerDimensionLines = [];
    this.dimensionLines = [];
    this.permanentAngles = [];

    // Redraw all paths
    let unitedPaths = drawAndUnitePaths(this.paths);
    if (unitedPaths) {
      unitedPaths.forEach(unitedPath => {
        unitedPath.fillColor = this.wallFillColor;
        unitedPath.strokeColor = this.wallStrokeColor;
        unitedPath.strokeWidth = 2;
        this.project.activeLayer.addChild(unitedPath);

        // For compound paths, draw inner dimensions
        if (unitedPath.className === 'CompoundPath') {
          const newInnerDimensions = this.dimensionService.drawAllInnerDimensionsForCompoundPath(
            this.project, unitedPath, this.WALL_THICKNESS, []
          );

          newInnerDimensions.forEach(dim => {
            this.innerDimensionLines.push(dim);
            if (this.showMeasurements) {
              this.project.activeLayer.addChild(dim);
            }
          });
        }
      });

      // Draw outer dimensions for each wall
      for (let i = 0; i < this.paths.length; i++) {
        const path = this.paths[i];
        const tempDimLines: any[] = [];

        const conn = this.pathConnections && this.pathConnections[i]
          ? this.pathConnections[i]
          : {middleConnection: false, relativeAngle: 0};

        if (!conn.middleConnection) {
          this.dimensionService.drawOuterDimensionLine(
            tempDimLines,
            path.segments[1].point,
            path.segments[2].point,
            this.WALL_THICKNESS,
            30,
            5,
            conn.relativeAngle || 0
          );

          tempDimLines.forEach(line => {
            this.permanentDimensionLines.push(line);
            if (this.showMeasurements) {
              this.project.activeLayer.addChild(line);
            }
          });
        }
      }

      // Draw angles between paths
      if (this.paths.length > 1) {
        for (let i = 1; i < this.paths.length; i++) {
          const prevPath = this.paths[i - 1];
          const currPath = this.paths[i];

          const currConn = this.pathConnections && this.pathConnections[i]
            ? this.pathConnections[i]
            : {
              startConnection: false,
              endConnection: false,
              middleConnection: false,
              relativeAngle: 0
            };

          const innerAngle = this.dimensionService.calculateInnerAngleByOrientation(
            prevPath, currPath, currConn.relativeAngle,
            currConn.startConnection, currConn.endConnection, currConn.middleConnection
          );

          const angleElements = this.dimensionService.displayAngle(
            this.project, innerAngle, prevPath, currPath,
            currConn.relativeAngle, currConn.startConnection,
            currConn.endConnection, currConn.middleConnection, this.showAngles
          );

          if (angleElements) {
            angleElements.forEach(element => {
              this.permanentAngles.push(element);
              if (this.showAngles) {
                this.project.activeLayer.addChild(element);
              }
            });
          }
        }
      }
    }
  }

  toggleEyeMenu() {
    this.eyeMenuOpen = !this.eyeMenuOpen;
  }

  onToggleMeasurements(value: boolean) {
    this.showMeasurements = value;

    if (this.showMeasurements) {
      this.permanentDimensionLines.forEach(line => {
        this.project.activeLayer.addChild(line);
      });

      if (this.showMeasurements) {
        this.innerDimensionLines.forEach(line => {
          this.project.activeLayer.addChild(line);
        });
      }

    } else {
      this.permanentDimensionLines.forEach(line => {
        line.remove();
      });
      this.innerDimensionLines.forEach(line => {
        line.remove();
      });
    }
  }

  onToggleAngles(value: boolean) {
    this.showAngles = value;
    if (this.showAngles) {
      this.permanentAngles.forEach(angleElement => {
        this.project.activeLayer.addChild(angleElement);
      });
    } else {
      this.permanentAngles.forEach(angleElement => {
        angleElement.remove();
      });
    }
  }

  setMode(mode: 'draw' | 'edit') {
    this.currentMode = mode;
    this.clearAllSelections();
  }

  onMouseDown(event: MouseEvent | TouchEvent) {
    this.eventDown(event);
  }

  private eventDown(event: MouseEvent | TouchEvent) {
    console.log('event.srcElement')
    console.log(event.srcElement)
    if (this.currentMode === 'edit') {
      const point = this.getProjectedMousePoint(event);

      for (let i = 0; i < this.originalPaths.length; i++) {
        const path = this.originalPaths[i];

        if (path.contains(point)) {
          this.draggedOriginalPath = path;
          this.draggedPathIndex = i;
          this.isDraggingPath = true;

          const center = path.bounds.center;
          this.dragOffset = center.subtract(point);
          this.dragStartPoint = center.clone();

          event.preventDefault();
          return;
        }
      }
    }


    if ('button' in event && event.button === 1) {
      this.isPanning = true;
      this.lastPanPoint = this.getProjectedMousePoint(event);
      return;
    }

    if ('touches' in event && event.touches.length === 2) {
      const center = this.getTouchCenter(event.touches);
      this.isPanning = true;
      this.lastPanPoint = this.project.view.viewToProject(center);
      return;
    }

    const projectedPoint = this.getProjectedMousePoint(event);
    this.startX = projectedPoint.x;
    this.startY = projectedPoint.y;
    this.clickStartPosition = projectedPoint;
  }

  // @ts-ignore
  private handlePathSelection(point: Point) {

    let foundPath = false;

    for (const path of this.originalPaths) {
      if (path.contains(point)) {
        foundPath = true;

        if (this.selectedPaths.has(path)) {
          this.deselectPath(path);
        } else {
          this.selectPath(path);
        }
        break;
      }
    }

    // clear all selections when clicking empty space ? maybe delete this
    if (!foundPath) {
      this.clearAllSelections();
    }
  }

  private selectPath(path: any) {
    this.selectedPaths.add(path);

    const highlightPath = path.clone();
    highlightPath.strokeColor = new Color(221 / 255, 51 / 255, 51 / 255); // --ion-color-primary
    highlightPath.strokeWidth = 2;
    highlightPath.fillColor = new Color(221 / 255, 51 / 255, 51 / 255, 0.2); // Hellerer Farbton

    this.project.activeLayer.addChild(highlightPath);

    this.selectedPathHighlights.set(path, highlightPath);
  }

  private deselectPath(path: any) {
    this.selectedPaths.delete(path);

    const highlight = this.selectedPathHighlights.get(path);
    if (highlight) {
      highlight.remove();
      this.selectedPathHighlights.delete(path);
    }
  }

  private clearAllSelections() {
    this.selectedPathHighlights.forEach(highlight => {
      highlight.remove();
    });

    this.selectedPaths.clear();
    this.selectedPathHighlights.clear();
  }

  onMouseMove(event: MouseEvent | TouchEvent) {
    this.eventMove(event);
  }

  private eventMove(event: MouseEvent | TouchEvent) {
    //TODO when moving walls they dont render at there new position
    //TODO dimensions are fucked up
    if (this.currentMode === 'edit' && this.isDraggingPath && this.draggedOriginalPath) {
      const point = this.getProjectedMousePoint(event);
      const newCenter = point.add(this.dragOffset!);
      const delta = newCenter.subtract(this.dragStartPoint!);
      this.dragStartPoint = newCenter;

      this.draggedOriginalPath.position = newCenter;

      this.originalPaths[this.draggedPathIndex] = this.draggedOriginalPath.clone();
      this.paths = this.originalPaths.map(p => p.clone());

      this.project.clear();

      const unitedPaths = drawAndUnitePaths(this.paths);
      if (unitedPaths) {
        unitedPaths.forEach(p => {
          p.fillColor = this.wallFillColor;
          p.strokeColor = this.wallStrokeColor;
          p.strokeWidth = 2;
          this.project.activeLayer.addChild(p);
        });
      }

      this.permanentDimensionLines.forEach(line => line.remove());
      this.innerDimensionLines.forEach(line => line.remove());
      this.dimensionLines = [];
      this.permanentDimensionLines = [];
      this.innerDimensionLines = [];

      for (let i = 0; i < this.paths.length; i++) {
        const path = this.paths[i];
        const conn = this.pathConnections[i] || {middleConnection: false, relativeAngle: 0};

        if (!conn.middleConnection) {
          this.dimensionService.drawOuterDimensionLine(
            this.dimensionLines,
            path.segments[1].point,
            path.segments[2].point,
            this.WALL_THICKNESS,
            30,
            5,
            conn.relativeAngle || 0
          );

          this.dimensionLines.forEach(line => {
            this.permanentDimensionLines.push(line);
            if (this.showMeasurements) {
              this.project.activeLayer.addChild(line);
            }
          });
        }
      }

      if (this.paths.length > 1 && this.showAngles) {
        this.permanentAngles.forEach(a => a.remove());
        this.permanentAngles = [];

        for (let i = 1; i < this.paths.length; i++) {
          const prev = this.paths[i - 1];
          const curr = this.paths[i];
          const conn = this.pathConnections[i] || {
            startConnection: false,
            endConnection: false,
            middleConnection: false,
            relativeAngle: 0
          };

          const innerAngle = this.dimensionService.calculateInnerAngleByOrientation(
            prev, curr, conn.relativeAngle,
            conn.startConnection, conn.endConnection, conn.middleConnection
          );

          const angleElements = this.dimensionService.displayAngle(
            this.project, innerAngle, prev, curr,
            conn.relativeAngle, conn.startConnection,
            conn.endConnection, conn.middleConnection,
            this.showAngles
          );

          if (angleElements) {
            angleElements.forEach(el => {
              this.permanentAngles.push(el);
              if (this.showAngles) {
                this.project.activeLayer.addChild(el);
              }
            });
          }
        }
      }

      event.preventDefault();
      return;
    }


    if (this.isPanning && this.lastPanPoint) {
      // @ts-ignore
      let newPanPoint: Point;

      if ('touches' in event && event.touches.length === 2) {
        const center = this.getTouchCenter(event.touches);
        newPanPoint = this.project.view.viewToProject(center);
      } else {
        newPanPoint = this.getProjectedMousePoint(event);
      }

      const delta = this.lastPanPoint.subtract(newPanPoint);
      this.project.view.translate(delta.negate());
      this.lastPanPoint = newPanPoint;

      return;
    }


    const projectedPoint = this.getProjectedMousePoint(event);
    let currentX = projectedPoint.x;
    let currentY = projectedPoint.y;


    if (!this.isDrawing && this.clickStartPosition) {
      const distance = Math.sqrt(
        Math.pow(currentX - this.clickStartPosition.x, 2) +
        Math.pow(currentY - this.clickStartPosition.y, 2)
      );

      if (distance > this.DRAG_THRESHOLD) {
        this.clickStartPosition = null;


        if (this.currentMode === 'draw') {
          this.clearAllSelections();
          this.startDrawing(this.startX, this.startY);
          this.isDrawing = true;
        }
      }
    }

    if (!this.isDrawing || this.isLocked) return;

    const snappedPos = this.snapToAngle(this.startX, this.startY, currentX, currentY);
    currentX = snappedPos.x;
    currentY = snappedPos.y;

    const snappedToParallel = this.findSnappingPoints(currentX, currentY);
    currentX = snappedToParallel.x;
    currentY = snappedToParallel.y;

    let currentPoint = new Point(currentX, currentY);
    let width = getLineLength(this.startPoint, currentPoint);

    if (width < this.MIN_LENGTH) {
      let angle = Math.atan2(currentY - this.startPoint.y, currentX - this.startPoint.x);

      // Calculate the new endpoint coordinates based on the minimum length and angle
      currentX = this.startPoint.x + Math.cos(angle)
      currentY = this.startPoint.y + Math.sin(angle)

      currentPoint = new Point(currentX, currentY);
      width = this.MIN_LENGTH;
    }
    const height = this.WALL_THICKNESS;
    let angle = calculateCurrentWallAngle(this.startPoint.x, this.startPoint.y, currentPoint.x, currentPoint.y);
    angle = Math.round(angle / this.ANGLE_SNAP) * this.ANGLE_SNAP;
    this.selectedAngle = angle;

    // Remove the current path before redrawing
    if (this.path) {
      this.path.remove();
    }

    // Clear previous angles and dimension lines
    this.permanentAngles.forEach(angle => angle.remove());
    this.permanentDimensionLines.forEach(line => line.remove());
    this.dimensionLines.forEach(line => line.remove());
    this.permanentAngles = [];
    this.permanentDimensionLines = [];
    this.dimensionLines = [];

    if (this.paths.length > 1 && this.closestPath) {
      const firstSegment = this.closestPath.firstSegment.point;
      const secondSegment = this.closestPath.segments[1].point;
      const thirdSegment = this.closestPath.segments[2].point;
      const lastSegment = this.closestPath.lastSegment.point;

      const prevAngle = Math.round(calculateCurrentWallAngle(
        this.closestPath.firstSegment.point.x,
        this.closestPath.segments[0].point.y,
        this.closestPath.segments[3].point.x,
        this.closestPath.lastSegment.point.y
      ));
      this.relativeAngle = angle - prevAngle;
      if (this.relativeAngle > 180) this.relativeAngle -= 360;
      if (this.relativeAngle < -180) this.relativeAngle += 360;

      const innerAngle = this.dimensionService.calculateInnerAngleByOrientation(this.closestPath, this.path, this.relativeAngle, this.startConnection, this.endConnection, this.middleConnection);
      this.isValidAngle = innerAngle >= 90
      if (!this.isValidAngle) {
        if (this.path) {
          this.path.strokeColor = new Color(1, 0, 0);
          this.path.strokeWidth = 2;
        }
        return;
      } else if (this.path) {
        this.path.strokeColor = this.wallStrokeColor;
      }
      if (this.startConnection) {
        // @ts-ignore
        //Oberhalb
        if (this.relativeAngle >= -180 && this.relativeAngle < 0) {
          this.startPoint = this.closestPath.segments[0].point;
          this.currentRect = new Rectangle(this.startPoint, new Size(width, height));
          this.currentRect.topLeft = this.startPoint;
          // Unterhalb
        } else if (this.relativeAngle <= 180 && this.relativeAngle >= 0) {
          this.startPoint = this.closestPath.segments[1].point;
          this.currentRect = new Rectangle(this.startPoint, new Size(width, height));
          this.currentRect.bottomLeft = this.startPoint;
        }

        this.path = new Path.Rectangle(this.currentRect);
        this.path.rotate(angle, this.startPoint);

      } else if (this.endConnection) {
        // @ts-ignore
        // Oberhalb
        if (this.relativeAngle > -180 && this.relativeAngle < 0) {
          this.startPoint = this.closestPath.segments[3].point;
          this.currentRect = new Rectangle(this.startPoint, new Size(width, height));
          this.currentRect.bottomLeft = this.startPoint;
          // Unterhalb
        } else if (this.relativeAngle >= 0 && this.relativeAngle <= 180) {
          this.startPoint = this.closestPath.segments[2].point;
          this.currentRect = new Rectangle(this.startPoint, new Size(width, height));
          this.currentRect.topLeft = this.startPoint;
        }
        this.path = new Path.Rectangle(this.currentRect);
        this.path.rotate(angle, this.startPoint);

      } else if (this.middleConnection) {
        const epsilon = 1e-5;
        let x = this.startPoint.x;
        let y = this.startPoint.y
        // Oberhalb
        if (this.relativeAngle > -180 && this.relativeAngle < 0) {
          if (Math.abs(thirdSegment.x - secondSegment.x) > epsilon) {
            y = secondSegment.y + ((thirdSegment.y - secondSegment.y) * (x - secondSegment.x)) / (thirdSegment.x - secondSegment.x);
          } else {
            x = secondSegment.x;
          }
          this.startPoint = new Point(x, y);
          this.currentRect = new Rectangle(this.startPoint, new Size(width, height));
          //TODO Fix "jumping" of rect when changing pos. topLeft or bottomLeft
          this.relativeAngle >= -90 && this.relativeAngle < 0 ? this.currentRect.topLeft = this.startPoint : this.currentRect.bottomLeft = this.startPoint;
          //Unterhalb
        } else if (this.relativeAngle >= 0 && this.relativeAngle <= 180) {
          if (Math.abs(thirdSegment.x - secondSegment.x) > epsilon) {
            y = firstSegment.y + ((lastSegment.y - firstSegment.y) * (x - firstSegment.x)) / (lastSegment.x - firstSegment.x);
          } else {
            x = firstSegment.x;
          }
          this.startPoint = new Point(x, y);
          this.currentRect = new Rectangle(this.startPoint, new Size(width, height));
          //TODO Fix "jumping" of rect when changing pos. topLeft or bottomLeft
          this.relativeAngle > 0 && this.relativeAngle < 90 ? this.currentRect.bottomLeft = this.startPoint : this.currentRect.topLeft = this.startPoint;
        }

        this.path = new Path.Rectangle(this.currentRect);
        this.path.rotate(angle, this.startPoint);
      }


    } else {
      this.currentRect = new Rectangle(this.startPoint, new Size(width, height));
      this.path = new Path.Rectangle(this.currentRect);
      this.path.rotate(angle, this.currentRect.topLeft);
    }

    this.path.fillColor = this.wallFillColor;
    this.path.strokeColor = this.wallStrokeColor;
    this.path.strokeWidth = 1;
    this.project.activeLayer.addChild(this.path);

    // Update the paths array
    if (this.paths.length > 0) {
      this.paths[this.paths.length - 1] = this.path;
    } else {
      this.paths.push(this.path);
    }

    // Draw permanent angles and dimensions
    if (this.paths.length > 1) {
      const innerAngle = this.dimensionService.calculateInnerAngleByOrientation(
        this.closestPath, this.path, this.relativeAngle,
        this.startConnection, this.endConnection, this.middleConnection
      );
      const angleElements = this.dimensionService.displayAngle(
        this.project, innerAngle, this.closestPath, this.path,
        this.relativeAngle, this.startConnection,
        this.endConnection, this.middleConnection, this.showAngles
      );
      if (angleElements) {
        angleElements.forEach(element => {
          this.permanentAngles.push(element);
          if (this.showAngles) {
            this.project.activeLayer.addChild(element);
          }
        });
      }
    }

    // Draw dimension lines
    if (!this.middleConnection) {
      this.dimensionService.drawOuterDimensionLine(
        this.dimensionLines,
        this.path.segments[1].point,
        this.path.segments[2].point,
        this.WALL_THICKNESS,
        30,
        5,
        this.relativeAngle
      );

      this.dimensionLines.forEach(line => {
        this.permanentDimensionLines.push(line);
        if (this.showMeasurements) {
          this.project.activeLayer.addChild(line);
        }
      });
    }

    if (this.showGuidelines) {
      this.guideLines.forEach(line => {
        this.project.activeLayer.addChild(line);
      });
    }
  }

  private startDrawing(x: number, y: number) {
    if (this.canvas) {
      const startPoint = new Point(x, y);
      this.startPoint = startPoint;

      // Check for connections with existing paths
      for (const path of this.paths) {
        const firstSegment = path.firstSegment.point;
        const secondSegment = path.segments[1].point;
        const thirdSegment = path.segments[2].point;
        const lastSegment = path.lastSegment.point;

        if (this.isNearPoint(this.startPoint, firstSegment, this.SNAP_RADIUS)) {
          this.startPoint = firstSegment;
          this.closestPath = path;
          this.startConnection = true;
          break;
        } else if (this.isNearPoint(this.startPoint, lastSegment, this.SNAP_RADIUS)) {
          this.startPoint = thirdSegment;
          this.closestPath = path;
          this.endConnection = true;
          break;
        } else if (path.contains(this.startPoint)) {
          let x = this.startPoint.x;
          let y = this.startPoint.y;
          const epsilon = 1e-5;
          if (Math.abs(thirdSegment.x - secondSegment.x) > epsilon) {
            y = secondSegment.y + ((thirdSegment.y - secondSegment.y) * (x - secondSegment.x)) / (thirdSegment.x - secondSegment.x);
          } else {
            x = secondSegment.x;
          }

          this.startPoint = new Point(x, y);

          this.closestPath = path;
          this.middleConnection = true;
          break;
        }
      }

      if (!this.startConnection && !this.endConnection && !this.middleConnection) {
        this.angleWheelPosition = new Point(x, y);
        this.selectedAngle = 0;
        this.drawAngleWheel(this.angleWheelPosition, this.selectedAngle);
      }

      this.path = new Path();
      this.path.fillColor = this.wallFillColor;
      this.path.strokeColor = this.wallStrokeColor;
      this.path.add(this.startPoint);
      this.paths.push(this.path);
    }
  }

  onMouseUp(event: MouseEvent | TouchEvent) {
    this.upEvent(event)
  }

  upEvent(event: MouseEvent | TouchEvent) {
    if (this.currentMode === 'edit' && this.isDraggingPath) {
      this.isDraggingPath = false;
      this.draggedOriginalPath = null;
      this.dragOffset = null;
      this.draggedPathIndex = -1;
      this.dragStartPoint = null;
      return;
    }

    if (this.isPanning) {
      this.isPanning = false;
      this.lastPanPoint = null;
      return;
    }

    if (!this.isDrawing && this.clickStartPosition) {
      const projectedPoint = this.getProjectedMousePoint(event);
      this.handlePathSelection(projectedPoint);

      this.clickStartPosition = null;
      return;
    }

    this.clickStartPosition = null;

    if (this.isDrawing && this.startPoint && this.path) {
      if ((this.startConnection || this.endConnection || this.middleConnection) && !this.isValidAngle) {
        this.cancelCurrentDrawing();
        return;
      }
      this.originalPaths.push(this.path.clone());

      const endProjectedPoint = this.getProjectedMousePoint(event);
      let endX = endProjectedPoint.x;
      let endY = endProjectedPoint.y;


      const snappedPos = this.snapToAngle(this.startX, this.startY, endX, endY);
      endX = snappedPos.x;
      endY = snappedPos.y;

      const endPoint = new Point(endX, endY);

      let angle = calculateCurrentWallAngle(this.startPoint.x, this.startPoint.y, endPoint.x, endPoint.y);
      angle = Math.round(angle / this.ANGLE_SNAP) * this.ANGLE_SNAP

      this.path = new Path.Rectangle(this.currentRect);

      if (this.startConnection) {
        this.relativeAngle < 0 && this.relativeAngle >= -180 ? this.path.rotate(angle, this.currentRect.topLeft) : this.path.rotate(angle, this.currentRect.bottomLeft)
      } else if (this.endConnection) {
        this.relativeAngle < 0 && this.relativeAngle >= -180 ? this.path.rotate(angle, this.currentRect.bottomLeft) : this.path.rotate(angle, this.currentRect.topLeft)
      } else if (this.middleConnection) {
        if (this.relativeAngle < 0 && this.relativeAngle >= -180) {
          if (this.relativeAngle < -90) {
            this.path.rotate(angle, this.currentRect.bottomLeft)
          } else if (this.relativeAngle >= -90) {
            this.path.rotate(angle, this.currentRect.topLeft)
          }
        } else if (this.relativeAngle >= 0 && this.relativeAngle <= 180) {
          if (this.relativeAngle < 90) {
            this.path.rotate(angle, this.currentRect.bottomLeft)
          } else if (this.relativeAngle >= 90) {
            this.path.rotate(angle, this.currentRect.topLeft)
          }
        }
      } else {
        this.path.rotate(angle, this.currentRect.topLeft)
      }
      this.path.strokeColor = new Color(0, 0, 0);
      this.path.strokeWidth = 0;

      this.project.clear()

      let unitedPaths = drawAndUnitePaths(this.paths);
      if (unitedPaths) {
        this.innerDimensionLines.forEach(line => line.remove());
        this.innerDimensionLines = [];
        unitedPaths.forEach(unitedPath => {
          if (unitedPath.className === 'CompoundPath') {
            const newInnerDimensions = this.dimensionService.drawAllInnerDimensionsForCompoundPath(this.project, unitedPath, this.WALL_THICKNESS, this.dimensionLines);
            this.innerDimensionLines.push(...newInnerDimensions);

            if (this.showMeasurements) {
              this.innerDimensionLines.forEach(line => {
                this.project.activeLayer.addChild(line);
              });
            }

            unitedPath.fillColor = this.wallFillColor;
            unitedPath.strokeColor = this.wallStrokeColor;
            unitedPath.strokeWidth = 2;
            this.project.activeLayer.addChild(unitedPath);
          } else {
            unitedPath.fillColor = this.wallFillColor;
            unitedPath.strokeColor = this.wallStrokeColor;
            unitedPath.strokeWidth = 2;
            this.project.activeLayer.addChild(unitedPath);
          }
        });
      }

      // Calculate and draw all angles and dimension lines
      this.permanentAngles.forEach(angle => angle.remove());
      this.permanentDimensionLines.forEach(line => line.remove());
      this.permanentAngles = [];
      this.permanentDimensionLines = [];

      // Draw outer dimensions for each wall
      for (let i = 0; i < this.paths.length; i++) {
        const path = this.paths[i];
        const conn = this.pathConnections[i] || {middleConnection: false, relativeAngle: 0};

        if (!conn.middleConnection) {
          this.dimensionService.drawOuterDimensionLine(
            this.dimensionLines,
            path.segments[1].point,
            path.segments[2].point,
            this.WALL_THICKNESS,
            30,
            5,
            conn.relativeAngle || 0
          );

          this.dimensionLines.forEach(line => {
            this.permanentDimensionLines.push(line);
            if (this.showMeasurements) {
              this.project.activeLayer.addChild(line);
            }
          });
        }
      }

      // Draw angles between paths
      if (this.paths.length > 1) {
        for (let i = 1; i < this.paths.length; i++) {
          const prevPath = this.paths[i - 1];
          const currPath = this.paths[i];

          const currConn = this.pathConnections[i] || {
            startConnection: false,
            endConnection: false,
            middleConnection: false,
            relativeAngle: 0
          };

          const innerAngle = this.dimensionService.calculateInnerAngleByOrientation(
            prevPath, currPath, currConn.relativeAngle,
            currConn.startConnection, currConn.endConnection, currConn.middleConnection
          );

          const angleElements = this.dimensionService.displayAngle(
            this.project, innerAngle, prevPath, currPath,
            currConn.relativeAngle, currConn.startConnection,
            currConn.endConnection, currConn.middleConnection, this.showAngles
          );

          if (angleElements) {
            angleElements.forEach(element => {
              this.permanentAngles.push(element);
              if (this.showAngles) {
                this.project.activeLayer.addChild(element);
              }
            });
          }
        }
      }

      this.startPoint = null;
      this.isDrawing = false;
      this.startConnection = false;
      this.endConnection = false;
      this.middleConnection = false;
      this.closestPath = null;

      if (this.planning) {
        const exportedJson = this.project.exportJSON({asString: true, precision: 2});
        this.planning.floor_plan_local = exportedJson;
        this.planning.floor_plan = exportedJson;

        this.plannerStorageService.createOrUpdatePlanning(this.planning).then(() => {
          this.planningService.updateFloorPlan(this.planningId!, exportedJson);
        });

        setTimeout(() => {
          this.project.clear();
          this.project.importJSON(this.planning!.floor_plan_local)
        }, 10)
      }

      setTimeout(() => {
        this.saveCanvasAsPng()
      }, 1000)
    }
  }

  angleAtSegment(path: any, index: number) {
    const seg = path.segments[index];
    const P = seg.point;
    const Pprev = path.segments[(index - 1 + path.segments.length) % path.segments.length].point;
    const Pnext = path.segments[(index + 1) % path.segments.length].point;

    const v1 = Pprev.subtract(P);
    const v2 = Pnext.subtract(P);
    let δ = v1.dot(v2) / (v1.length * v2.length);
    δ = Math.max(-1, Math.min(1, δ));

    return Math.round(Math.acos(δ) * 180 / Math.PI);
  }

  // @ts-ignore
  private drawAngleWheel(center: Point, currentAngle: number) {
    const radius = 100;
    const wheel = new Path.Circle({
      center: center,
      radius: radius,
      strokeColor: new Color(0.5, 0.5, 0.5, 0.9),
      strokeWidth: 2,
      dashArray: [6, 6],
      fillColor: new Color(1, 1, 1, 0.4)
    });
    this.project.activeLayer.addChild(wheel);

    const segmentAngle = 15;
    const totalSegments = 360 / segmentAngle;
    const labelOffset = 20;

    for (let i = 0; i < totalSegments; i++) {
      const deg = i * segmentAngle;
      const rad = (deg - 90) * (Math.PI / 180);

      const innerPoint = new Point(
        center.x + (radius - 8) * Math.cos(rad),
        center.y + (radius - 8) * Math.sin(rad)
      );
      const outerPoint = new Point(
        center.x + (radius + 4) * Math.cos(rad),
        center.y + (radius + 4) * Math.sin(rad)
      );
      const mark = new Path.Line({
        from: innerPoint,
        to: outerPoint,
        strokeColor: new Color(0.3, 0.3, 0.3, 0.9),
        strokeWidth: 1.5
      });
      this.project.activeLayer.addChild(mark);

      const textPosition = new Point(
        center.x + (radius + labelOffset) * Math.cos(rad),
        center.y + (radius + labelOffset) * Math.sin(rad)
      );

      const text = new PointText({
        point: textPosition,
        content: `${deg}°`,
        fillColor: 'black',
        fontSize: 12,
        justification: 'center'
      });


      this.project.activeLayer.addChild(text);

    }
  }

  // Add a new method to cancel the current drawing
  private cancelCurrentDrawing() {
    // Remove the current path
    if (this.path) {
      this.path.remove();
    }

    // Remove the last path from the paths array
    this.paths.pop();

    // Reset drawing state
    this.startPoint = null;
    this.isDrawing = false;
    this.startConnection = false;
    this.endConnection = false;
    this.middleConnection = false;
    this.closestPath = null;
    this.isValidAngle = true;

    // Redraw the project
    this.project.clear();

    let unitedPaths = drawAndUnitePaths(this.paths);
    if (unitedPaths) {
      unitedPaths.forEach(unitedPath => {
        unitedPath.fillColor = this.wallFillColor;
        unitedPath.strokeColor = this.wallStrokeColor;
        unitedPath.strokeWidth = 2;
        this.project.activeLayer.addChild(unitedPath);
      });
    }

    // Re-add dimensions and angles
    if (this.showMeasurements) {
      this.permanentDimensionLines.forEach(line => {
        this.project.activeLayer.addChild(line);
      });

      this.innerDimensionLines.forEach(line => {
        this.project.activeLayer.addChild(line);
      });
    }

    if (this.showAngles) {
      this.permanentAngles.forEach(angleElement => {
        this.project.activeLayer.addChild(angleElement);
      });
    }
  }

  private isNearPoint(point1: Coordinate, point2: Coordinate, radius: number): boolean {
    return this.graph.getDistanceNew(point1, point2) <= radius;
  }

  private findSnappingPoints(currentX: number, currentY: number): { x: number, y: number } {
    let snappedX = currentX;
    let snappedY = currentY;
    this.guideLines.forEach(line => line.remove());
    this.guideLines = [];

    for (const path of this.paths) {
      if (path === this.path) {
        continue;
      }

      path.segments.forEach((segment: { point: any; }) => {
        const point = segment.point;

        if (Math.abs(currentY - point.y) < 15) {
          const guideLine = new Path.Line(
            new Point(0, point.y),
            new Point(this.canvas!.width, point.y)
          );
          guideLine.strokeColor = new Color(this.GUIDE_LINE_COLOR);
          guideLine.dashArray = [5, 5];
          this.guideLines.push(guideLine);
          snappedY = point.y;
        }

        if (Math.abs(currentX - point.x) < 15) {
          const guideLine = new Path.Line(
            new Point(point.x, 0),
            new Point(point.x, this.canvas!.height)
          );
          guideLine.strokeColor = new Color(this.GUIDE_LINE_COLOR);
          guideLine.dashArray = [5, 5];
          this.guideLines.push(guideLine);
          snappedX = point.x;
        }
      });
    }

    return {x: snappedX, y: snappedY};
  }

  private snapToAngle(startX: number, startY: number, endX: number, endY: number): { x: number, y: number } {
    let angle = Math.atan2(endY - startY, endX - startX);
    let degrees = Math.round(angle * (180 / Math.PI));

    degrees = Math.round(degrees / this.ANGLE_SNAP) * this.ANGLE_SNAP;
    angle = degrees * (Math.PI / 180);

    const length = Math.sqrt(
      Math.pow(endX - startX, 2) +
      Math.pow(endY - startY, 2)
    );

    return {
      x: startX + length * Math.cos(angle),
      y: startY + length * Math.sin(angle)
    };
  }

  //TODO Save deleted paths for reset and redoLastStep functions
  deleteSelectedPaths() {
    if (this.selectedPaths.size === 0) return;

    const deletedPaths = this.originalPaths.filter(path => this.selectedPaths.has(path));
    this.originalPaths = this.originalPaths.filter(path => !this.selectedPaths.has(path));

    deletedPaths.forEach(path => {
      this.undoneOriginalPaths.push(path);
    })

    if (this.pathConnections && this.pathConnections.length > 0) {
      const deletedConnections = this.pathConnections.filter(conn =>
        !deletedPaths.includes(conn.path)
      );
      deletedConnections.forEach(conn => {
        this.undonePathConnections.push(conn);
      })
    }

    this.project.clear();

    this.permanentDimensionLines.forEach(line => line.remove());
    this.innerDimensionLines.forEach(line => line.remove());
    this.permanentAngles.forEach(angle => angle.remove());

    this.permanentDimensionLines = [];
    this.innerDimensionLines = [];
    this.dimensionLines = [];
    this.permanentAngles = [];

    this.clearAllSelections();

    this.paths = [];
    this.originalPaths.forEach(path => {
      this.paths.push(path.clone());
    })

    // Redraw everything if there are paths left
    if (this.paths.length > 0) {
      let unitedPaths = drawAndUnitePaths(this.paths);

      if (unitedPaths) {
        unitedPaths.forEach(unitedPath => {
          unitedPath.fillColor = this.wallFillColor;
          unitedPath.strokeColor = this.wallStrokeColor;
          unitedPath.strokeWidth = 2;
          this.project.activeLayer.addChild(unitedPath);

          // For compound paths, draw inner dimensions
          if (unitedPath.className === 'CompoundPath') {
            const newInnerDimensions = this.dimensionService.drawAllInnerDimensionsForCompoundPath(
              this.project, unitedPath, this.WALL_THICKNESS, []
            );

            newInnerDimensions.forEach(dim => {
              this.innerDimensionLines.push(dim);
              if (this.showMeasurements) {
                this.project.activeLayer.addChild(dim);
              }
            });
          }
        });

        // Draw outer dimensions for each wall
        for (let i = 0; i < this.paths.length; i++) {
          const path = this.paths[i];
          const tempDimLines: any[] = [];

          const conn = this.pathConnections && this.pathConnections[i]
            ? this.pathConnections[i]
            : {middleConnection: false, relativeAngle: 0};

          if (!conn.middleConnection) {
            this.dimensionService.drawOuterDimensionLine(
              tempDimLines,
              path.segments[1].point,
              path.segments[2].point,
              this.WALL_THICKNESS,
              30,
              5,
              conn.relativeAngle || 0
            );

            tempDimLines.forEach(line => {
              this.permanentDimensionLines.push(line);
              if (this.showMeasurements) {
                this.project.activeLayer.addChild(line);
              }
            });
          }
        }

        // Draw angles between paths
        if (this.paths.length > 1) {
          for (let i = 1; i < this.paths.length; i++) {
            const prevPath = this.paths[i - 1];
            const currPath = this.paths[i];

            const currConn = this.pathConnections && this.pathConnections[i]
              ? this.pathConnections[i]
              : {
                startConnection: false,
                endConnection: false,
                middleConnection: false,
                relativeAngle: 0
              };

            const innerAngle = this.dimensionService.calculateInnerAngleByOrientation(
              prevPath, currPath, currConn.relativeAngle,
              currConn.startConnection, currConn.endConnection, currConn.middleConnection
            );

            const angleElements = this.dimensionService.displayAngle(
              this.project, innerAngle, prevPath, currPath,
              currConn.relativeAngle, currConn.startConnection,
              currConn.endConnection, currConn.middleConnection, this.showAngles
            );

            if (angleElements) {
              angleElements.forEach(element => {
                this.permanentAngles.push(element);
                if (this.showAngles) {
                  this.project.activeLayer.addChild(element);
                }
              });
            }
          }
        }
      }
    }

    if (this.planning) {
      const exportedJson = this.project.exportJSON({asString: true, precision: 2});
      this.planning.floor_plan_local = exportedJson;
      this.planning.floor_plan = exportedJson;

      this.plannerStorageService.createOrUpdatePlanning(this.planning).then(() => {
        this.planningService.updateFloorPlan(this.planningId!, exportedJson);
      });

      setTimeout(() => {
        this.project.clear();
        this.project.importJSON(this.planning!.floor_plan_local)
      }, 10)
    }
  }

  resetLastStep() {
    if (this.originalPaths.length === 0) return;

    // Save the path being removed to the undone stack
    const lastPath = this.originalPaths.pop();
    this.undoneOriginalPaths.push(lastPath);

    // Save connection info if available
    if (this.pathConnections && this.pathConnections.length > 0) {
      const lastConn = this.pathConnections.pop();
      // @ts-ignore
      this.undonePathConnections.push(lastConn);
    }

    // remove the visual elements from the canvas
    this.permanentDimensionLines.forEach(line => line.remove());
    this.innerDimensionLines.forEach(line => line.remove());

    // Clear the arrays
    this.permanentDimensionLines = [];
    this.innerDimensionLines = [];
    this.dimensionLines = [];

    // Clear permanent angles
    this.permanentAngles.forEach(angle => angle.remove());
    this.permanentAngles = [];

    // Rebuild paths
    this.paths = [];
    this.originalPaths.forEach(path => {
      this.paths.push(path.clone());
    });

    this.project.clear();

    if (this.paths.length > 0) {
      let unitedPaths = drawAndUnitePaths(this.paths);

      if (unitedPaths) {
        unitedPaths.forEach(unitedPath => {
          unitedPath.fillColor = this.wallFillColor;
          unitedPath.strokeColor = this.wallStrokeColor;
          unitedPath.strokeWidth = 2;
          this.project.activeLayer.addChild(unitedPath);

          if (unitedPath.className === 'CompoundPath') {
            const newInnerDimensions = this.dimensionService.drawAllInnerDimensionsForCompoundPath(
              this.project, unitedPath, this.WALL_THICKNESS, []
            );

            newInnerDimensions.forEach(dim => {
              this.innerDimensionLines.push(dim);

              if (this.showMeasurements) {
                this.project.activeLayer.addChild(dim);
              }
            });
          }
        });

        for (let i = 0; i < this.paths.length; i++) {
          const path = this.paths[i];
          const tempDimLines: any[] = [];

          const conn = this.pathConnections && this.pathConnections[i]
            ? this.pathConnections[i]
            : {middleConnection: false, relativeAngle: 0};

          if (!conn.middleConnection) {
            this.dimensionService.drawOuterDimensionLine(
              tempDimLines,
              path.segments[1].point,
              path.segments[2].point,
              this.WALL_THICKNESS,
              30,
              5,
              conn.relativeAngle || 0
            );


            tempDimLines.forEach(line => {
              this.permanentDimensionLines.push(line);


              if (this.showMeasurements) {
                this.project.activeLayer.addChild(line);
              }
            });
          }
        }

        if (this.paths.length > 1) {
          for (let i = 1; i < this.paths.length; i++) {
            const prevPath = this.paths[i - 1];
            const currPath = this.paths[i];

            const currConn = this.pathConnections && this.pathConnections[i]
              ? this.pathConnections[i]
              : {
                startConnection: false,
                endConnection: false,
                middleConnection: false,
                relativeAngle: 0
              };

            const innerAngle = this.dimensionService.calculateInnerAngleByOrientation(
              prevPath, currPath, currConn.relativeAngle,
              currConn.startConnection, currConn.endConnection, currConn.middleConnection
            );

            const angleElements = this.dimensionService.displayAngle(
              this.project, innerAngle, prevPath, currPath,
              currConn.relativeAngle, currConn.startConnection,
              currConn.endConnection, currConn.middleConnection, this.showAngles
            );

            if (angleElements) {
              angleElements.forEach(element => {
                this.permanentAngles.push(element);
                if (this.showAngles) {
                  this.project.activeLayer.addChild(element);
                }
              });
            }
          }
        }
      }
    }

    // Clear the canvas if no paths remain
    if (this.paths.length === 0) {
      this.project.clear();
    }
  }

  redoLastStep() {
    if (this.undoneOriginalPaths.length === 0) return;

    const pathToRestore = this.undoneOriginalPaths.pop();
    this.originalPaths.push(pathToRestore);

    if (this.undonePathConnections.length > 0) {
      const connToRestore = this.undonePathConnections.pop();
      // @ts-ignore
      this.pathConnections.push(connToRestore);
    }

    this.permanentDimensionLines.forEach(line => line.remove());
    this.innerDimensionLines.forEach(line => line.remove());
    this.permanentAngles.forEach(angle => angle.remove());

    this.permanentDimensionLines = [];
    this.innerDimensionLines = [];
    this.dimensionLines = [];
    this.permanentAngles = [];

    this.paths = [];
    this.originalPaths.forEach(path => {
      this.paths.push(path.clone());
    });

    this.project.clear();

    // Re-unite and redraw all paths
    if (this.paths.length > 0) {
      let unitedPaths = drawAndUnitePaths(this.paths);

      if (unitedPaths) {
        unitedPaths.forEach(unitedPath => {
          unitedPath.fillColor = this.wallFillColor;
          unitedPath.strokeColor = this.wallStrokeColor;
          unitedPath.strokeWidth = 2;
          this.project.activeLayer.addChild(unitedPath);

          // For compound paths, draw inner dimensions
          if (unitedPath.className === 'CompoundPath') {
            const newInnerDimensions = this.dimensionService.drawAllInnerDimensionsForCompoundPath(
              this.project, unitedPath, this.WALL_THICKNESS, []
            );

            newInnerDimensions.forEach(dim => {
              this.innerDimensionLines.push(dim);
              if (this.showMeasurements) {
                this.project.activeLayer.addChild(dim);
              }
            });
          }
        });

        // Draw outer dimensions for each wall
        for (let i = 0; i < this.paths.length; i++) {
          const path = this.paths[i];
          const tempDimLines: any[] = [];

          const conn = this.pathConnections && this.pathConnections[i]
            ? this.pathConnections[i]
            : {middleConnection: false, relativeAngle: 0};

          if (!conn.middleConnection) {
            this.dimensionService.drawOuterDimensionLine(
              tempDimLines,
              path.segments[1].point,
              path.segments[2].point,
              this.WALL_THICKNESS,
              30,
              5,
              conn.relativeAngle || 0
            );

            tempDimLines.forEach(line => {
              this.permanentDimensionLines.push(line);
              if (this.showMeasurements) {
                this.project.activeLayer.addChild(line);
              }
            });
          }
        }

        // Draw angles between paths
        if (this.paths.length > 1) {
          for (let i = 1; i < this.paths.length; i++) {
            const prevPath = this.paths[i - 1];
            const currPath = this.paths[i];

            const currConn = this.pathConnections && this.pathConnections[i]
              ? this.pathConnections[i]
              : {
                startConnection: false,
                endConnection: false,
                middleConnection: false,
                relativeAngle: 0
              };

            const innerAngle = this.dimensionService.calculateInnerAngleByOrientation(
              prevPath, currPath, currConn.relativeAngle,
              currConn.startConnection, currConn.endConnection, currConn.middleConnection
            );

            //TODO Fix for sequence: draw wall then startConn. or endConn. -> middleConn. -> resetLastStep -> redoLastStep => angle of middleConn. missing (multiple sequences fail)
            const angleElements = this.dimensionService.displayAngle(
              this.project, innerAngle, prevPath, currPath,
              currConn.relativeAngle, currConn.startConnection,
              currConn.endConnection, currConn.middleConnection, this.showAngles
            );

            if (angleElements) {
              angleElements.forEach(element => {
                this.permanentAngles.push(element);
                if (this.showAngles) {
                  this.project.activeLayer.addChild(element);
                }
              });
            }
          }
        }
      }
    }

    // Clear the canvas if no paths remain
    if (this.paths.length === 0) {
      this.project.clear();
    }
  }

  public zoomReset() {
    if (!this.project || !this.canvas) return;
    this.project.view.zoom = .25;
    this.scale = .25;
    this.project.view.center = new Point(this.canvas.width / 2, this.canvas.height / 2);
  }


  // @ts-ignore
  private getProjectedMousePoint(event: MouseEvent | TouchEvent): Point {
    const {x, y} = this.getPosition(event);
    const rect = this.canvas!.getBoundingClientRect();

    // For touch events, handle them differently
    if ('touches' in event || event.type.startsWith('touch')) {
      // Direct calculation for touch events
      const canvasX = x - rect.left;
      const canvasY = y - rect.top;
      const raw = new Point(canvasX, canvasY);

      // Debug logging for touch events
      if (this.devMode) {
        console.log('Touch position:', {x, y}, 'Canvas position:', {canvasX, canvasY}, 'Projected:', this.project.view.viewToProject(raw));
      }

      return this.project.view.viewToProject(raw);
    }

    // For mouse events, use the existing calculation
    const canvasX = x - rect.left;
    const canvasY = y - rect.top;
    const raw = new Point(canvasX, canvasY);
    return this.project.view.viewToProject(raw);
  }

  // @ts-ignore
  private getTouchCenter(touches: TouchList): Point {
    const x = (touches[0].clientX + touches[1].clientX) / 2;
    const y = (touches[0].clientY + touches[1].clientY) / 2;
    return new Point(x, y);
  }

  //TODO Fix wrong startPoint for mouseClick after zooming
  onWheel(event: WheelEvent) {
    event.preventDefault();
    const mousePos = new Point(event.offsetX, event.offsetY);
    const zoomFactor = event.deltaY < 0 ? 1.1 : 1 / 1.1;
    this.applyZoom(zoomFactor, mousePos);
  }

  private applyZoom(zoomFactor: number, center: paper.Point) {
    const newZoom = this.project.view.zoom * zoomFactor;
    if (newZoom > this.MAX_ZOOM && zoomFactor > 1) {
      this.project.view.zoom = this.MAX_ZOOM;
      this.scale = this.MAX_ZOOM;
      return;
    }
    if (newZoom < this.MIN_ZOOM && zoomFactor < 1) {
      this.project.view.zoom = this.MIN_ZOOM;
      this.scale = this.MIN_ZOOM;
      return;
    }

    this.project.view.scale(zoomFactor, center);
    this.scale = this.project.view.zoom;
  }

  private redrawCanvas() {
    const canvas = this.canvasRef.nativeElement;
    this.ctx.clearRect(0, 0, canvas.width, canvas.height);
    this.ctx.save();
    this.ctx.scale(this.scale, this.scale);
    this.walls.forEach(w => this.ctx = this.drawingService.drawRawWall(w, this.ctx, this.walls))
    this.graphs.forEach(g => {
      if (g.nodes.size > 0) { // Check if nodes map is not empty
        const firstEntry = g.nodes.entries().next().value; // Get the first [key, value] pair
        if (firstEntry && firstEntry[1]) { // Ensure firstEntry and its value exist
          const firstNode = firstEntry[1]; // This is the first node object
          const dfsResult = g.dfs(firstNode.coordinate);
          this.ctx = this.drawingService.drawLine(this.ctx, dfsResult);
          g.nodes.forEach(n => {
            this.ctx.fillStyle = 'purple';
            this.ctx.beginPath();
            this.ctx.arc(n.coordinate.x, n.coordinate.y, 5, 0, Math.PI * 2);
            this.ctx.fillText(`(${n.coordinate.x},${n.coordinate.y})`, n.coordinate.x + 10, n.coordinate.y - 5);
            this.ctx.fill();
          });
        }
      }
    })
    this.ctx.restore();
  }

  private getPosition(event: MouseEvent | TouchEvent) {
    if ('touches' in event && event.touches.length > 0) {
      return {x: event.touches[0].clientX, y: event.touches[0].clientY};
    } else if ('clientX' in event) {
      return {x: event.clientX, y: event.clientY};
    } else if (event.type === 'touchend' || event.type === 'touchcancel') {
      return {x: event.changedTouches[0].clientX, y: event.changedTouches[0].clientY};
    }
    return {x: 0, y: 0};
  }

  async drawSavedUnitedPaths() {
    if (!this.canvas) this.canvas = this.canvasRef.nativeElement;
    if (!this.ctx) this.ctx = this.canvas!.getContext('2d')!;
    if (!this.project) this.project = new Project(this.canvas);

    if (this.planning && this.planning.floor_plan) {
      const json = this.planning.floor_plan;
      if (!json) {
        return;
      }
      this.project.clear();
      this.project.importJSON(json);
      this.originalPaths = this.project.activeLayer.children.filter((i: any) => i instanceof Path).filter((i: any) => i.closed)
      this.paths = drawAndUnitePaths(this.originalPaths);
      setTimeout(() => {
        this.saveCanvasAsPng()
      }, 1000)
    }
  }

  reloadWindow() {
    document.location.reload();
  }

  saveCanvasAsPng() {
    const srcCanvas = this.canvas!
    const ctx = this.ctx!;
    const {width: w, height: h} = srcCanvas;
    // Ensure context and canvas are valid before proceeding
    if (!ctx || !srcCanvas) {
      console.error("Canvas context or element is not available.");
      return;
    }
    const pixels = ctx.getImageData(0, 0, w, h).data;

    let top = h, left = w, right = 0, bottom = 0;
    for (let y = 0; y < h; y++) {
      for (let x = 0; x < w; x++) {
        const alpha = pixels[(y * w + x) * 4 + 3];
        if (alpha !== 0) {
          if (x < left) left = x;
          if (x > right) right = x;
          if (y < top) top = y;
          if (y > bottom) bottom = y;
        }
      }
    }
    if (right < left || bottom < top) {
      return;
    }

    const cropW = right - left + 1;
    const cropH = bottom - top + 1;
    const outCanvas = document.createElement('canvas');
    outCanvas.width = cropW + 32;
    outCanvas.height = cropH + 32;
    const octx = outCanvas.getContext('2d')!;
    octx.fillStyle = '#f3f4f6';      // ↞ pick any color you like
    octx.fillRect(0, 0, cropW + 32, cropH + 32);

    octx.drawImage(
      srcCanvas,
      left, top, cropW, cropH,   // src rect
      16, 16, cropW, cropH    // dst rect
    );

    outCanvas.toBlob(async (blob) => {
      if (!blob) return;
      const url = URL.createObjectURL(blob);
      if (this.planningId) {
        const img = await blobToBase64(blob);
        await this.plannerStorageService.save2dImageToPlanningInfo(this.planningId, img);
      }
    }, 'image/png');
  }

  pinchOut(event: any) {
    if (!event || !event.scale || !event.center) return;
    // Mit requestAnimationFrame optimieren, um zu häufige DOM-Zugriffe zu vermeiden
    requestAnimationFrame(() => {
      const canvas = this.canvasRef?.nativeElement;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const pinchX = event.center.x - rect.left;
      const pinchY = event.center.y - rect.top;

      const pinchCenter = new Point(pinchX, pinchY);
      const scale = event.scale;

      const rawScale = event.scale;
      const adjustedScale = 1 + (rawScale - 1) * 0.01;
      if (Math.abs(adjustedScale - 1) > 0.01) {
        this.applyZoom(adjustedScale, pinchCenter);
      }
    });
  }

  pinchIn(event: any) {
    if (!event || !event.scale || !event.center) return;
    // Mit requestAnimationFrame optimieren, um zu häufige DOM-Zugriffe zu vermeiden
    requestAnimationFrame(() => {
      const canvas = this.canvasRef?.nativeElement;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const pinchX = event.center.x - rect.left;
      const pinchY = event.center.y - rect.top;

      const pinchCenter = new Point(pinchX, pinchY);
      const scale = event.scale;

      const rawScale = event.scale;
      const adjustedScale = 1 - (rawScale + 1) * 0.01;
      if (Math.abs(adjustedScale - 1) > 0.01) {
        this.applyZoom(adjustedScale, pinchCenter);
      }
    });
  }

  lastPan: { x: number, y: number } | null = null;

  onTouchStart(event: TouchEvent) {
    event.preventDefault();
    if (event.touches.length === 1) {
      this.eventDown(event);
    } else if (event.touches.length === 2) {
      this.isPinching = true;
      this.touchStartPoints = Array.from(event.touches).map(touch => ({
        x: touch.clientX,
        y: touch.clientY
      }));

      // Calculate initial distance between touch points
      this.lastTouchDistance = this.getTouchDistance(this.touchStartPoints[0], this.touchStartPoints[1]);

      // Store initial pan position
      this.lastPan = {
        x: (this.touchStartPoints[0].x + this.touchStartPoints[1].x) / 2,
        y: (this.touchStartPoints[0].y + this.touchStartPoints[1].y) / 2
      };
    }
  }

  onTouchMove(event: TouchEvent) {
    event.preventDefault();
    if (event.touches.length === 1) {
      this.eventMove(event);
    } else if (event.touches.length === 2 && this.isPinching) {
      const currentPoints = Array.from(event.touches).map(touch => ({
        x: touch.clientX,
        y: touch.clientY
      }));
      const currentCenter = {
        x: (currentPoints[0].x + currentPoints[1].x) / 2,
        y: (currentPoints[0].y + currentPoints[1].y) / 2
      };

      if (this.lastPan) {
        const dx = currentCenter.x - this.lastPan.x;
        const dy = currentCenter.y - this.lastPan.y;
        const view = this.project.view;
        const scaleFactor = 1 / view.zoom;
        view.translate(new Point(dx * scaleFactor, dy * scaleFactor));
        this.lastPan = currentCenter;
      }
      // Handle pinch zoom
      const currentDistance = this.getTouchDistance(currentPoints[0], currentPoints[1]);
      const scale = currentDistance / this.lastTouchDistance;

      if (scale < 1) {
        const canvas = this.canvasRef?.nativeElement;
        if (canvas) {
          const rect = canvas.getBoundingClientRect();
          const pinchCenter = new Point(
            currentCenter.x - rect.left,
            currentCenter.y - rect.top
          );
          this.applyZoom(scale, pinchCenter);
        }
      } else if (Math.abs(scale - 1) > 0.01) {
        const canvas = this.canvasRef?.nativeElement;
        if (canvas) {
          const rect = canvas.getBoundingClientRect();
          const pinchCenter = new Point(
            currentCenter.x - rect.left,
            currentCenter.y - rect.top
          );
          this.applyZoom(scale, pinchCenter);
        }
      }
      this.lastTouchDistance = currentDistance;
    }
  }

  onTouchEnd(event: TouchEvent) {
    if (event.touches.length < 2) {
      this.upEvent(event)
      this.isPinching = false;
      this.lastPan = null;
      this.touchStartPoints = [];
      this.lastTouchDistance = 0;
    }
  }

  private getTouchDistance(point1: { x: number, y: number }, point2: { x: number, y: number }): number {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }
}
